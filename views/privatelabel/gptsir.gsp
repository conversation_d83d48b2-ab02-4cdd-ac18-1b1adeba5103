<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT Sir - AI Companion Inside Every Textbook</title>
    <link rel="stylesheet" href="/assets/bookgpt/gptsirSite.css">
    <!-- Bootstrap CSS for carousel -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS for carousel -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
<!-- Header -->
<header class="header">
    <div class="container">
        <nav class="nav">
            <a href="/" class="logo">
                <img src="/assets/gptsir/gptsirai-logo.png" alt="GPT Sir Logo" style="width: 150px;">
            </a>
            <ul class="nav-links">
                <li><a href="/sp/gptsir/store">Books</a></li>
                <li><a href="#benefits">Benefits</a></li>
                <li><a href="#demo">Demo</a></li>
                <li><a href="/sp/gptsir/store" class="store-btn">Store</a></li>
            </ul>
        </nav>
    </div>
</header>

<!-- Loading Icon -->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<!-- Hero Section with Banner Carousel -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1>AI Companion Inside Every Textbook</h1>
                <a href="/sp/gptsir/store" class="hero-btn">Shop GPT Sir Books</a>
            </div>
            <div class="hero-visual">
                <!-- Banner Carousel for Desktop -->
                <div id="slider-desktop" class="carousel slide banner-carousel-desktop" data-ride="carousel" data-interval="5000">
                    <ol class="carousel-indicators" id="slider-desktop-indicators"></ol>
                    <div class="carousel-inner" id="slider-desktop-views"></div>
                    <a class="carousel-control-prev" href="#slider-desktop" role="button" data-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="carousel-control-next" href="#slider-desktop" role="button" data-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                </div>

                <!-- Banner Carousel for Mobile -->
                <div id="slider-mobile" class="carousel slide banner-carousel-mobile" data-ride="carousel" data-interval="5000">
                    <ol class="carousel-indicators" id="slider-mobile-indicators"></ol>
                    <div class="carousel-inner" id="slider-mobile-views"></div>
                    <a class="carousel-control-prev" href="#slider-mobile" role="button" data-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="carousel-control-next" href="#slider-mobile" role="button" data-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                </div>

                <!-- Fallback Visual Elements (shown when no banners) -->
                <div class="fallback-visual">

                </div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="benefits" id="benefits">
    <div class="container">
        <h2>Benefits</h2>
        <div class="benefits-grid">
            <div class="benefit-card doubt-solving">
                <div class="benefit-icon">❓</div>
                <div class="benefit-text">
                    <h3>Instant Doubt Solving</h3>
                </div>
            </div>
            <div class="benefit-card ai-tutor">
                <div class="benefit-icon">🤖</div>
                <div class="benefit-text">
                    <h3>AI Tutor</h3>
                </div>
            </div>
            <div class="benefit-card step-by-step">
                <div class="benefit-icon">📋</div>
                <div class="benefit-text">
                    <h3>Step-by-Step Concept Walkthroughs</h3>
                </div>
            </div>
            <div class="benefit-card adaptive-practice">
                <div class="benefit-icon">🎯</div>
                <div class="benefit-text">
                    <h3>Interactive MCQs</h3>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="stats">
            <div class="stat">
                <div class="stat-number">2.3M+</div>
                <div class="stat-label">AI Interactions</div>
            </div>
            <div class="stat">
                <div class="stat-number">100+</div>
                <div class="stat-label">Books</div>
            </div>
            <div class="stat">
                <div class="stat-number">1M+</div>
                <div class="stat-label">MCQs</div>
            </div>
        </div>

        <!-- Categories -->
        <h2>Categories</h2>
        <div class="categories-grid">
            <div class="category-card ncert">
                <div class="category-content">
                    <div class="category-icon ncert-icon">📚</div>
                    <div class="category-text">
                        <h3>NCERT</h3>
                    </div>
                </div>
                <button class="shop-now-btn">Shop Now</button>
            </div>
            <div class="category-card state-boards">
                <div class="category-content">
                    <div class="category-icon state-boards-icon">🏫</div>
                    <div class="category-text">
                        <h3>State Boards</h3>
                    </div>
                </div>
                <button class="shop-now-btn">Shop Now</button>
            </div>
            <div class="category-card jee-neet">
                <div class="category-content">
                    <div class="category-icon jee-neet-icon">🎯</div>
                    <div class="category-text">
                        <h3>JEE NEET</h3>
                    </div>
                </div>
                <button class="shop-now-btn">Shop Now</button>
            </div>
        </div>

        <!-- Campus Section -->
        <div class="campus-section" id="demo">
            <div class="campus-demo">
                <div class="phone-mockup">
                    <div class="phone-screen">
                        <div class="phone-content">
                            <div class="chat-bubble">💬</div>
                        </div>
                    </div>
                </div>
                <ul class="campus-features">
                    <li>Context-aware chat interface</li>
                    <li>Tailored to board curriculum</li>
                    <li>Dashboard for progress tracking</li>
                </ul>
            </div>
            <div class="campus-cta">
                <h2>Run an AI Campus in 48 hours</h2>
                <div class="contact-card">
                    <div class="contact-details">
                        <span class="contact-label">Contact Us</span>
                        <a href="tel:+918088443860" class="contact-number">+91 8088443860</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Banner carousel variables and functions
    var siteId = "${session?.siteId ?: 'gptsir'}";
    var privateLabelBanner = "${session?.bannerImage ?: ''}";
    var checkDescBanners = false;
    var checkMobBanners = false;
    var templateDesktop = "";
    var templateMobile = "";
    var defaultDeskImageUrl = "/assets/gptsir/gptsirwhite.svg";
    var defaultMobImageUrl = "/assets/gptsir/gptsirwhite.svg";

    function showBanners(data) {
        setTimeout(function () {
            $(".loading-icon").addClass("hidden");
        }, 2000);

        if(data && data.status == "OK") {
            var banners = data.banners.reverse();
            $('#slider-desktop-views,#slider-mobile-views').empty();

            // Banner slider
            $.each(banners, function (i, v) {
                var item = v;
                var htmlStr = '';
                var htmlStrMobile = '';
                var indicatorStr = '';
                var indicatorStrMobile = '';
                var imageDescUrl = "/wonderpublish/showImage?id=" + v.id + "&fileName=" + v.imagePath + "&imgType=webp";
                var imageMobUrl = "/wonderpublish/showImage?id="+v.id+"&fileName="+v.imagePathMobile + "&imgType=webp";

                if(v.bookTitle) {
                    var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
                }
                var bookHyperLink = bookTitle + "/ebook-details?siteName=gptsir&bookId=" + v.bookId + "&preview=true";

                var actionLink = v.action;
                var serverURL = window.location.origin;
                actionLink = serverURL + '/' + actionLink;

                indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
                indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";

                if(v.bookId) {
                    htmlStr += '<div class="carousel-item">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'" alt="Banner">' +
                        '</a>' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageMobUrl+'" alt="Banner">' +
                        '</a>' +
                        '</div>';
                } else if (v.action) {
                    htmlStr += '<div class="carousel-item">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageDescUrl+'" alt="Banner">' +
                        '</a>' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<img src="'+imageMobUrl+'" alt="Banner">' +
                        '</a>' +
                        '</div>';
                } else {
                    htmlStr += '<div class="carousel-item">' +
                        '<img src="'+imageDescUrl+'" alt="Banner">' +
                        '</div>';
                    htmlStrMobile += '<div class="carousel-item">' +
                        '<img src="'+imageMobUrl+'" alt="Banner">' +
                        '</div>';
                }

                // If desktop banners are available
                if(v.imagePath) {
                    checkDescBanners = true;
                    $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
                    $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
                }

                // If mobile banners are available
                if(v.imagePathMobile) {
                    checkMobBanners = true;
                    $('#slider-mobile-views').append(htmlStrMobile).find('.carousel-item:first-child').addClass('active');
                    $('#slider-mobile-indicators').append(indicatorStrMobile).find('li:first-child').addClass('active');
                }
            });

            // Show carousel and hide fallback
            if(checkDescBanners || checkMobBanners) {
                $('.banner-carousel-desktop, .banner-carousel-mobile').show();
                $('.fallback-visual').hide();

                // Initialize carousel auto-play
                setTimeout(function() {
                    $('#slider-desktop').carousel('cycle');
                    $('#slider-mobile').carousel('cycle');
                }, 1000);
            }
        } else {
            checkDescBanners = false;
            checkMobBanners = false;
        }

        // Showing fallback visual when no banners
        if(!checkDescBanners && !checkMobBanners) {
            $('.banner-carousel-desktop, .banner-carousel-mobile').hide();
            $('.fallback-visual').show();
        }
    }

    // If desktop banner images are empty calling this function
    function emptyDesktopBannerUI(defaultImage) {
        var emptyBannerDesk = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'" alt="Default Banner">' +
            '</div>';
        return emptyBannerDesk;
    }

    // If mobile banner images are empty calling this function
    function emptyMobileBannerUI(defaultImage) {
        var emptyBannerMob = '<div class="carousel-item active">' +
            '<img src="'+defaultImage+'" alt="Default Banner">' +
            '</div>';
        return emptyBannerMob;
    }

    function getBannerDetails(siteId) {
        $(".loading-icon").removeClass("hidden");
        // Simulated AJAX call - replace with actual implementation
        $.ajax({
            url: '/wonderpublish/getBannerdetails',
            type: 'GET',
            data: { siteId: siteId },
            success: function(data) {
                showBanners(data);
            },
            error: function() {
                // Show fallback on error
                setTimeout(function () {
                    $(".loading-icon").addClass("hidden");
                    $('.banner-carousel-desktop, .banner-carousel-mobile').hide();
                    $('.fallback-visual').show();
                }, 1000);
            }
        });
    }

    function replaceAll(str, find, replace) {
        if (str == undefined) return str;
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }

    // Initialize banner carousel
    $(document).ready(function() {
        // Initially hide carousels and show fallback
        $('.banner-carousel-desktop, .banner-carousel-mobile').hide();
        $('.fallback-visual').show();

        // Configure carousel settings
        $('.carousel').carousel({
            interval: 5000,
            pause: 'hover',
            wrap: true
        });

        // Try to get banners
        getBannerDetails(siteId);
    });

    // Add smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add hover effects for cards
    document.querySelectorAll('.benefit-card, .category-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>

<!-- Footer Section -->
<footer class="gptsir-footer">
    <div class="container">
        <div class="footer-content">
            <!-- Logo and About Section -->
            <div class="footer-section footer-about">
                <div class="footer-logo">
                    <img src="./gptsirai-logo.png" alt="GPT Sir Logo" class="footer-logo-img">
                </div>
                <p class="footer-description">
                    AI Companion Inside Every Textbook - Revolutionizing education with intelligent tutoring and interactive learning experiences.
                </p>
                <div class="footer-social">
                    <% if(session["facebookLink"]) { %>
                    <a href="${session["facebookLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <% } %>
                    <% if(session["twitterLink"]) { %>
                    <a href="${session["twitterLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <% } %>
                    <% if(session["instagramLink"]) { %>
                    <a href="${session["instagramLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <% } %>
                    <% if(session["linkedinLink"]) { %>
                    <a href="${session["linkedinLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <% } %>
                    <% if(session["youtubeLink"]) { %>
                    <a href="${session["youtubeLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <% } %>
                    <% if(session["whatsappLink"]) { %>
                    <a href="${session["whatsappLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <% } %>
                    <% if(session["telegramLink"]) { %>
                    <a href="${session["telegramLink"]}" target="_blank" class="social-link">
                        <i class="fab fa-telegram-plane"></i>
                    </a>
                    <% } %>
                </div>
            </div>

            <!-- Quick Links Section -->
            <div class="footer-section footer-links">
                <h3 class="footer-title">Quick Links</h3>
                <ul class="footer-menu">
                    <li><a href="/sp/gptsir/store">Books</a></li>
                    <li><a href="#benefits">Benefits</a></li>
                    <li><a href="#demo">Demo</a></li>
                    <li><a href="/sp/gptsir/store">Store</a></li>
                </ul>
            </div>

            <!-- Categories Section -->
            <%if(!"Yes".equals(""+session["disableStore"])){%>
            <div class="footer-section footer-categories">
                <h3 class="footer-title">Categories</h3>
                <ul class="footer-menu" id="footerCategoryLinks">
                    <!-- Dynamic categories will be populated here -->
                </ul>
            </div>
            <%}%>

            <!-- Contact Section -->
            <div class="footer-section footer-contact">
                <h3 class="footer-title">Contact Us</h3>
                <div class="contact-info">
                    <% if(session['enableContactus'] && "true".equals(session['enableContactus']) && !"".equals(session["mobileNumber"])){%>
                    <%
                        String number = session["mobileNumber"]
                        def numbers = number.split(",\\s*")
                    %>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div class="contact-details">
                            <% numbers.eachWithIndex { mobileNumber, index -> %>
                            <a href="tel:${mobileNumber.trim()}">${mobileNumber.trim()}</a>
                            <% } %>
                        </div>
                    </div>
                    <% } else { %>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div class="contact-details">
                            <a href="tel:+918088443860">+91 8088443860</a>
                        </div>
                    </div>
                    <% } %>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div class="contact-details">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- App Download Section -->
            <% if(session["playStore"] || session["appStore"]) { %>
            <div class="footer-section footer-apps">
                <h3 class="footer-title">Download App</h3>
                <div class="app-links">
                    <% if(session["playStore"]) { %>
                    <a href="${session["playStore"]}" target="_blank" class="app-link">
                        <img src="${assetPath(src: 'playstore.png')}" alt="Google Play Store">
                    </a>
                    <% } %>
                    <% if(session["appStore"]) { %>
                    <a href="${session["appStore"]}" target="_blank" class="app-link">
                        <img src="${assetPath(src: 'appstore.png')}" alt="App Store">
                    </a>
                    <% } %>
                </div>
            </div>
            <% } %>
        </div>
    </div>
    <!-- Footer Bottom -->
    <div class="footer-bottom">
        <div class="footer-bottom-content">
            <p class="copy-right-text-footer"></p>
            <div class="footer-policies">
                <a href="${session["privacyPolicy"] ? session["privacyPolicy"] : "https://www.wonderslate.com/funlearn/privacy"}" target="_blank">Privacy Policy</a>
                <span class="separator">|</span>
                <a href="${session["termsCondition"] ? session["termsCondition"] : "https://www.wonderslate.com/funlearn/termsandconditions"}" target="_blank">Terms & Conditions</a>
            </div>
        </div>
    </div>
</footer>

<!-- Mobile Footer Navigation -->
<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>

<script>
    // Dynamic Year in Footer
    var strDate = new Date();
    var shortYear = strDate.getFullYear();
    var nextYear = (new Date().getFullYear()+1);
    var twoDigitYear = nextYear.toString().substr(-2);
    $('.copy-right-text-footer').html('Copyright &copy; ' + shortYear +'-'+twoDigitYear+". <span> Powered by Wonderslate</span> ");

    // Dynamic Categories (using dummy data for GPT Sir)
    var activeCategories = [
        {level: "NCERT"},
        {level: "State Boards"},
        {level: "JEE NEET"},
        {level: "CBSE"},
        {level: "ICSE"}
    ];

    var footerCategoryLinks="";
    for (var i = 0; i < activeCategories.length; i++) {
        footerCategoryLinks +="<li><a href='/sp/gptsir/store?level="+replaceAll(activeCategories[i].level.replace('&', '~'),' ','-')+"'>"+activeCategories[i].level+"</a></li>";
    }
    if (document.getElementById("footerCategoryLinks")!=null){
        document.getElementById("footerCategoryLinks").innerHTML=footerCategoryLinks;
    }

    // Mobile Footer Navigation
    var mobileFooterNav =
        '<div class="d-flex row justify-content-around w-100">' +
        '    <a href="/sp/gptsir/store" class="ebooks-menu d-flex align-items-center col">' +
        '        <i class="fas fa-book"></i>' +
        '        <p>Store</p>' +
        '    </a>' +
        '    <a href="/sp/gptsir/store" class="home-menu d-flex align-items-center col">' +
        '        <i class="fas fa-home"></i>' +
        '        <p>Home</p>' +
        '    </a>' +
        '</div>';

    $(document).ready(function(){
        if(document.getElementById('mobile-footer-nav')) {
            document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav;
        }
    });
</script>

<!-- Font Awesome for Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

</body>
</html>